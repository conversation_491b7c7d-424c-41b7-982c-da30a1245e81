name: base
channels:
  - file:///tmp/conda-pkgs
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_kmp_llvm
  - anyio=3.5.0=py310h06a4308_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py310h7f8727e_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - attrs=22.1.0=py310h06a4308_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - beautifulsoup4=4.12.2=py310h06a4308_0
  - bleach=4.1.0=pyhd3eb1b0_0
  - boltons=23.0.0=py310h06a4308_0
  - brotlipy=0.7.0=py310h7f8727e_1002
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2023.05.30=h06a4308_0
  - certifi=2023.5.7=py310h06a4308_0
  - cffi=1.15.1=py310h5eee18b_3
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - comm=0.1.2=py310h06a4308_0
  - conda=23.5.0=py310h06a4308_0
  - conda-package-handling=2.1.0=py310h06a4308_0
  - conda-package-streaming=0.8.0=py310h06a4308_0
  - cryptography=39.0.1=py310h9ce1e76_2
  - debugpy=1.5.1=py310h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - dlenv-pytorch-1-13-gpu=1.0.20230616=py310h003b471_0
  - entrypoints=0.4=py310h06a4308_0
  - executing=0.8.3=pyhd3eb1b0_0
  - icu=58.2=he6710b0_3
  - idna=3.4=py310h06a4308_0
  - ipython=8.12.0=py310h06a4308_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - jedi=0.18.1=py310h06a4308_1
  - jinja2=3.1.2=py310h06a4308_0
  - jsonpatch=1.32=pyhd3eb1b0_0
  - jsonpointer=2.1=pyhd3eb1b0_0
  - jsonschema=4.17.3=py310h06a4308_0
  - jupyter_client=8.1.0=py310h06a4308_0
  - jupyter_core=5.3.0=py310h06a4308_0
  - jupyter_events=0.6.3=py310h06a4308_0
  - jupyter_server=2.5.0=py310h06a4308_0
  - jupyter_server_terminals=0.4.4=py310h06a4308_1
  - jupyterlab_pygments=0.1.2=py_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=13.1.0=he5830b7_0
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libuv=1.44.2=h5eee18b_0
  - libxml2=2.10.3=hcbfbd50_0
  - libxslt=1.1.37=h2085143_0
  - llvm-openmp=14.0.6=h9e868ea_0
  - markupsafe=2.1.1=py310h7f8727e_0
  - matplotlib-inline=0.1.6=py310h06a4308_0
  - mistune=0.8.4=py310h7f8727e_1000
  - nb_conda=2.2.1=py310h06a4308_1
  - nb_conda_kernels=2.3.1=py310h06a4308_0
  - nbclassic=0.5.5=py310h06a4308_0
  - nbclient=0.5.13=py310h06a4308_0
  - nbconvert=6.5.4=py310h06a4308_0
  - nbformat=5.7.0=py310h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.5.6=py310h06a4308_0
  - nodejs=18.16.0=h2d74bed_0
  - notebook=6.5.4=py310h06a4308_0
  - notebook-shim=0.2.2=py310h06a4308_0
  - openssl=3.0.9=h7f8727e_0
  - packaging=23.0=py310h06a4308_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pip=23.1.2=py310h06a4308_0
  - platformdirs=2.5.2=py310h06a4308_0
  - pluggy=1.0.0=py310h06a4308_1
  - prometheus_client=0.14.1=py310h06a4308_0
  - prompt-toolkit=3.0.36=py310h06a4308_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pycosat=0.6.4=py310h5eee18b_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pygments=2.15.1=py310h06a4308_1
  - pyopenssl=23.0.0=py310h06a4308_0
  - pyrsistent=0.18.0=py310h7f8727e_0
  - pysocks=1.7.1=py310h06a4308_0
  - python=3.10.12=h955ad1f_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py310h06a4308_0
  - python-json-logger=2.0.7=py310h06a4308_0
  - python_abi=3.10=2_cp310
  - pyyaml=6.0=py310h5eee18b_1
  - pyzmq=25.1.0=py310h6a678d5_0
  - readline=8.2=h5eee18b_0
  - requests=2.29.0=py310h06a4308_0
  - rfc3339-validator=0.1.4=py310h06a4308_0
  - rfc3986-validator=0.1.1=py310h06a4308_0
  - ruamel.yaml=0.17.21=py310h5eee18b_0
  - ruamel.yaml.clib=0.2.7=py310h1fa729e_1
  - send2trash=1.8.0=pyhd3eb1b0_1
  - setuptools=67.8.0=py310h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sniffio=1.2.0=py310h06a4308_1
  - sqlite=3.41.2=h5eee18b_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - terminado=0.17.1=py310h06a4308_0
  - tinycss2=1.2.1=py310h06a4308_0
  - tk=8.6.12=h1ccaba5_0
  - toolz=0.12.0=py310h06a4308_0
  - tornado=6.2=py310h5eee18b_0
  - tqdm=4.65.0=py310h2f386ee_0
  - traitlets=5.7.1=py310h06a4308_0
  - typing-extensions=4.6.3=py310h06a4308_0
  - typing_extensions=4.6.3=py310h06a4308_0
  - tzdata=2023c=h04d1e81_0
  - urllib3=1.26.16=py310h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - websocket-client=0.58.0=py310h06a4308_4
  - wheel=0.38.4=py310h06a4308_0
  - xz=5.4.2=h5eee18b_0
  - yaml=0.2.5=h7b6447c_0
  - zeromq=4.3.4=h2531618_0
  - zlib=1.2.13=h5eee18b_0
  - zstandard=0.19.0=py310h5eee18b_0
  - pip:
      - absl-py==1.4.0
      - aiohttp==3.8.4
      - aiohttp-cors==0.7.0
      - aiorwlock==1.3.0
      - aiosignal==1.3.1
      - ansiwrap==0.8.4
      - async-timeout==4.0.2
      - babel==2.12.1
      - backoff==2.2.1
      - beatrix-jupyterlab==2023.615.192330
      - blessed==1.20.0
      - blobfile==2.0.2
      - cachetools==5.3.1
      - click==8.1.3
      - clip==1.0
      - cloud-tpu-client==0.10
      - cloudpickle==2.2.1
      - colorful==0.5.5
      - contourpy==1.1.0
      - cycler==0.11.0
      - cython==0.29.35
      - dacite==1.8.1
      - db-dtypes==1.1.1
      - deprecated==1.2.14
      - distlib==0.3.6
      - dm-tree==0.1.8
      - docker==6.1.3
      - docstring-parser==0.15
      - fastapi==0.97.0
      - filelock==3.12.2
      - fire==0.5.0
      - fonttools==4.40.0
      - frozenlist==1.3.3
      - fsspec==2023.6.0
      - ftfy==6.1.1
      - gcsfs==2023.6.0
      - gitdb==4.0.10
      - gitpython==3.1.31
      - google-api-core==1.34.0
      - google-api-python-client==1.8.0
      - google-auth==2.20.0
      - google-auth-httplib2==0.1.0
      - google-auth-oauthlib==1.0.0
      - google-cloud-aiplatform==1.26.0
      - google-cloud-artifact-registry==1.8.1
      - google-cloud-bigquery==3.11.1
      - google-cloud-bigquery-storage==2.20.0
      - google-cloud-core==2.3.2
      - google-cloud-datastore==1.15.5
      - google-cloud-language==2.10.0
      - google-cloud-monitoring==2.15.0
      - google-cloud-resource-manager==1.10.1
      - google-cloud-storage==2.9.0
      - google-crc32c==1.5.0
      - google-resumable-media==2.5.0
      - googleapis-common-protos==1.59.1
      - gpustat==1.0.0
      - greenlet==2.0.2
      - grpc-google-iam-v1==0.12.6
      - grpcio==1.51.3
      - grpcio-status==1.48.2
      - gymnasium==0.26.3
      - gymnasium-notices==0.0.1
      - h11==0.14.0
      - htmlmin==0.1.12
      - httplib2==0.22.0
      - humanize==4.7.0
      - imagehash==4.3.1
      - imageio==2.31.1
      - importlib-metadata==6.0.1
      - ipykernel==6.23.2
      - ipython-sql==0.5.0
      - ipywidgets==8.0.6
      - jaraco-classes==3.2.3
      - jeepney==0.8.0
      - joblib==1.2.0
      - json5==0.9.14
      - jupyter-client==7.4.9
      - jupyter-http-over-ws==0.0.8
      - jupyter-server==1.24.0
      - jupyter-server-mathjax==0.2.6
      - jupyter-server-proxy==4.0.0
      - jupyterlab==3.4.8
      - jupyterlab-git==0.41.0
      - jupyterlab-server==2.23.0
      - jupyterlab-widgets==3.0.7
      - jupytext==1.14.6
      - keyring==23.13.1
      - keyrings-google-artifactregistry-auth==1.1.2
      - kfp==1.8.22
      - kfp-pipeline-spec==0.1.16
      - kfp-server-api==1.8.5
      - kiwisolver==1.4.4
      - kubernetes==25.3.0
      - lazy-loader==0.2
      - llvmlite==0.40.1rc1
      - lxml==4.9.3
      - lz4==4.3.2
      - markdown-it-py==2.2.0
      - matplotlib==3.7.1
      - mdit-py-plugins==0.4.0
      - mdurl==0.1.2
      - more-itertools==9.1.0
      - msgpack==1.0.5
      - multidict==6.0.4
      - multimethod==1.9.1
      - nbdime==3.2.0
      - networkx==3.1
      - notebook-executor==0.2
      - numba==0.57.0
      - numpy==1.23.5
      - nvidia-cublas-cu11==**********
      - nvidia-cuda-nvrtc-cu11==11.7.99
      - nvidia-cuda-runtime-cu11==11.7.99
      - nvidia-cudnn-cu11==********
      - nvidia-ml-py==11.495.46
      - oauth2client==4.1.3
      - oauthlib==3.2.2
      - opencensus==0.11.2
      - opencensus-context==0.1.3
      - opentelemetry-api==1.18.0
      - opentelemetry-exporter-otlp==1.18.0
      - opentelemetry-exporter-otlp-proto-common==1.18.0
      - opentelemetry-exporter-otlp-proto-grpc==1.18.0
      - opentelemetry-exporter-otlp-proto-http==1.18.0
      - opentelemetry-proto==1.18.0
      - opentelemetry-sdk==1.18.0
      - opentelemetry-semantic-conventions==0.39b0
      - pandas==1.5.3
      - pandas-profiling==3.6.6
      - papermill==2.4.0
      - patsy==0.5.3
      - phik==0.12.3
      - pillow==9.5.0
      - plotly==5.15.0
      - prettytable==3.8.0
      - proto-plus==1.22.2
      - protobuf==3.20.3
      - psutil==5.9.3
      - py-spy==0.3.14
      - pyarrow==12.0.1
      - pyasn1==0.5.0
      - pyasn1-modules==0.3.0
      - pycryptodomex==3.18.0
      - pydantic==1.10.9
      - pyjwt==2.7.0
      - pyngrok==6.0.0
      - pyparsing==3.0.9
      - pytz==2023.3
      - pywavelets==1.4.1
      - ray==2.5.0
      - ray-cpp==2.5.0
      - regex==2023.6.3
      - requests-oauthlib==1.3.1
      - requests-toolbelt==0.10.1
      - retrying==1.3.4
      - rich==13.4.2
      - scikit-image==0.21.0
      - scikit-learn==1.2.2
      - scipy==1.10.1
      - seaborn==0.12.2
      - secretstorage==3.3.3
      - shap-e==0.0.0
      - shapely==1.8.5.post1
      - simpervisor==1.0.0
      - smart-open==6.3.0
      - smmap==5.0.0
      - soupsieve==2.4.1
      - sqlalchemy==2.0.16
      - sqlparse==0.4.4
      - starlette==0.27.0
      - statsmodels==0.14.0
      - strip-hints==0.1.10
      - tabulate==0.9.0
      - tangled-up-in-unicode==0.2.0
      - tenacity==8.2.2
      - tensorboardx==2.6
      - termcolor==2.3.0
      - textwrap3==0.9.2
      - threadpoolctl==3.1.0
      - tifffile==2023.4.12
      - toml==0.10.2
      - tomli==2.0.1
      - torch==1.13.1
      - torchvision==0.14.1
      - typeguard==2.13.3
      - typer==0.9.0
      - uritemplate==3.0.1
      - uvicorn==0.22.0
      - virtualenv==20.21.0
      - visions==0.7.5
      - webencodings==0.5.1
      - widgetsnbextension==4.0.7
      - wordcloud==1.9.2
      - wrapt==1.15.0
      - yarl==1.9.2
      - ydata-profiling==4.2.0
prefix: /opt/conda
