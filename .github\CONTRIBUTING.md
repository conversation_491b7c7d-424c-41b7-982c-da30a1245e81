## How to contribute to the OpenRouter Runner

### Want to add a new model?

We'd love to see you add more models to the Runner! If you're interested in contributing, please follow the section on [Adding a New Model](../README.md/#adding-new-models) to start adding more Open Source models to OpenRouter! In addition, please adhere to our [code of conduct](./CODE_OF_CONDUCT.md) to maintain a healthy and welcoming community.

### Did you find a bug?

- **Ensure the bug was not already reported** by searching on GitHub under [issues](https://github.com/OpenRouterTeam/openrouter-runner/issues)

- If you're unable to find an open issue addressing the problem, open a [new one](https://github.com/OpenRouterTeam/openrouter-runner/issues/new). Be sure to include a title and clear description, as much relevant information as possible, and a **code sample** or an **executable test case** demonstrating the expected behavior that is not occurring.

### Did you write a patch that fixes a bug?

- Open a new GitHub pull request with the patch.

- Ensure the PR description clearly describes the problem and solution. Include the relevant issue number if applicable.

### Do you have questions about the source code?

Ask any question about how to use OpenRouter in our [Discord](https://discord.gg/fVyRaUDgxW).
