name: Release (prod)

on:
  push:
    branches: [main]
    paths: ['modal/**', 'pyproject.toml', 'poetry.lock']

jobs:
  release:
    name: Release (prod)
    runs-on: ubuntu-latest
    env:
      MODAL_TOKEN_ID: ${{ secrets.MODAL_TOKEN_ID }}
      MODAL_TOKEN_SECRET: ${{ secrets.MODAL_TOKEN_SECRET }}

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - uses: abatilo/actions-poetry@v2

      - name: Install dependencies
        run: poetry install --no-root

      - name: Deploy
        run: |
          cd modal
          poetry run modal config set-environment main

          # TODO: Re-enable when we're confident in the download action bug fix
          #
          # echo "Downloading models..."
          # poetry run modal run runner::download

          echo "Deploying..."
          poetry run modal deploy runner
