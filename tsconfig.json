{"compilerOptions": {"target": "es2017", "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitReturns": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "incremental": true, "verbatimModuleSyntax": true}, "include": ["next-env.d.ts", "**/*.ts"]}