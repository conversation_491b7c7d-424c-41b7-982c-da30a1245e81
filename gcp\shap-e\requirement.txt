anyio==3.7.0
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
arrow==1.2.3
asttokens==2.2.1
attrs==23.1.0
backcall==0.2.0
beautifulsoup4==4.12.2
bleach==6.0.0
blobfile==2.0.2
certifi==2023.5.7
cffi==1.15.1
charset-normalizer==3.1.0
click==8.1.3
clip @ git+https://github.com/openai/CLIP.git
cmake==3.26.4
comm==0.1.3
contourpy==1.1.0
cycler==0.11.0
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
exceptiongroup==1.1.1
executing==1.2.0
fastapi==0.97.0
fastjsonschema==2.17.1
filelock==3.12.2
fire==0.5.0
fonttools==4.40.0
fqdn==1.5.1
ftfy==6.1.1
h11==0.14.0
humanize==4.6.0
idna==3.4
imageio==2.31.1
importlib-metadata==6.6.0
importlib-resources==5.12.0
ipykernel==6.23.2
ipython
ipython-genutils==0.2.0
ipywidgets==7.6.5
isoduration==20.11.0
jedi==0.18.2
Jinja2==3.1.2
jsonpointer==2.3
jsonschema==4.17.3
jupyter-events==0.6.3
jupyter_client==8.2.0
jupyter_core==5.3.1
jupyter_server==2.6.0
jupyter_server_terminals==0.4.4
jupyterlab-pygments==0.2.2
jupyterlab-widgets==3.0.7
kiwisolver==1.4.4
lazy_loader==0.2
lit==16.0.6
lxml==4.9.2
MarkupSafe==2.1.3
matplotlib==3.7.1
matplotlib-inline==0.1.6
mistune==2.0.5
mpmath==1.3.0
nbclassic==1.0.0
nbclient==0.8.0
nbconvert==7.5.0
nbformat==5.9.0
nest-asyncio==1.5.6
networkx==3.1
notebook==6.5.4
notebook_shim==0.2.3
numpy==1.24.3
nvidia-cublas-cu11==**********
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cudnn-cu11==********
nvidia-cufft-cu11==*********
nvidia-curand-cu11==**********
nvidia-cusolver-cu11==********
nvidia-cusparse-cu11==*********
nvidia-nccl-cu11==2.14.3
nvidia-nvtx-cu11==11.7.91
overrides==7.3.1
packaging==23.1
pandocfilters==1.5.0
parso==0.8.3
pexpect==4.8.0
pickleshare==0.7.5
Pillow==9.5.0
platformdirs==3.5.3
prometheus-client==0.17.0
prompt-toolkit==3.0.38
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.21
pycryptodomex==3.18.0
pydantic==1.10.9
Pygments==2.15.1
pyngrok==6.0.0
pyparsing==3.0.9
pyrsistent==0.19.3
python-dateutil==2.8.2
python-json-logger==2.0.7
PyWavelets==1.4.1
PyYAML==6.0
pyzmq==25.1.0
regex==2023.6.3
requests==2.31.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
scikit-image==0.21.0
scipy==1.10.1
Send2Trash==1.8.2
shap-e @ git+https://github.com/openai/shap-e.git
six==1.16.0
sniffio==1.3.0
soupsieve==2.4.1
stack-data==0.6.2
starlette==0.27.0
sympy==1.12
termcolor==2.3.0
terminado==0.17.1
tifffile==2023.4.12
tinycss2==1.2.1
torch==2.0.1
torchvision==0.15.2
tornado==6.3.2
tqdm==4.65.0
traitlets==5.9.0
triton==2.0.0
typing_extensions==4.6.3
uri-template==1.2.0
urllib3==2.0.3
uvicorn==0.22.0
wcwidth==0.2.6
webcolors==1.13
webencodings==0.5.1
websocket-client==1.5.3
widgetsnbextension==3.5.2
zipp==3.15.0